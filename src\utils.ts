import { Injectable } from '@nestjs/common';
import { Repository, Connection, JsonContains, DataSource } from 'typeorm';
import {DataBaseService, BaseLibraryService, ReferenceDataSettings, ReferenceDataPGPMMapping, ReferenceDataKeys, Constants as libConstants, AwsUtilityV3 } from '@bryzos/base-library';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Constants } from 'src/Constants';
import { addDays, format, parseISO } from 'date-fns';
import * as moment from 'moment-timezone';
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';
// import { ExceptionService } from './handlers/exception.service';
import { ExceptionService, BryzosLogger} from '@bryzos/extended-widget-library';
import { CompanyBuyNowPayLater } from '@bryzos/extended-widget-library';
import { HttpService } from '@nestjs/axios';
const axios = require('axios');
import { spawn } from 'child_process';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';
import { ExcelSheetWrapper } from './export-excel-sheet-wrapper';

const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);
export interface ExportedFile {
  fileName: string;
  sheetName: string;
}
@Injectable()
export class Utils {
  private dbServiceObj = new DataBaseService();

  constructor(
    private readonly baseLibraryService: BaseLibraryService,
    private readonly httpService : HttpService,
    private readonly awsUtility: AwsUtilityV3,
    private readonly excelSheetWrapper: ExcelSheetWrapper,

    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(ReferenceDataPGPMMapping) private readonly referenceDataPGPMMappingRepository: Repository<ReferenceDataPGPMMapping>,
    @InjectRepository(ReferenceDataKeys) private readonly referenceDataKeysRepository: Repository<ReferenceDataKeys>,
    @InjectDataSource() private readonly dataSource: DataSource,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>

  ) { }

  async createTransactionLogData(source,userId,referenceId,event,data) {

    const currentDateTime = new Date();
    const utcTime = currentDateTime.toISOString();


    const logData = {
        source,
        user_id: userId,
        reference_id: referenceId,
        event,
        process: Constants.LOGGING_INFO,
        date_time: utcTime,
        data,
      };


    let logResponse = BryzosLogger.log(JSON.stringify(logData),process.env.ADMIN_DASHBOARD_ACH_BNPL_LOGGLY_LOG_TAG);

  }

  convertUtcToCst(dateTime) {
    let datetime = null;
    if (moment.utc(dateTime).unix() && moment(dateTime).utc(true).format('YYYY-MM-DD HH:mm:ss')) {
      datetime = moment(dateTime).utc(true).clone().tz('America/Chicago');
    }
    return datetime;
  }

  createLogData = async (filenameLogging, referenceId, logEvent, data) => {
    let dateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    let logData = { source: filenameLogging, referenceId: referenceId, event: logEvent, process: Constants.LOGGING_INFO, date_time: dateTime, data: data };
    BryzosLogger.log(JSON.stringify(logData), process.env.ADMIN_DASHBOARD_CASS_LOGGLY_LOG_TAG);
  };

  async getEncodedData(dataToEncode) {
    let encryptedRes = '';

    //encrypt vendor id
    let encryptionKey = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.ENCRYPTION_KEY);
    let encryptionType = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.ENCRYPTION_TYPE);

    let iv = randomBytes(16);

    const key = (await promisify(scrypt)(encryptionKey, 'salt', 32)) as Buffer;
    const cipher = createCipheriv(encryptionType, key, iv);

    let encryptedResponse = Buffer.concat([cipher.update(dataToEncode), cipher.final()]);

    encryptedRes = encryptedResponse.toString('base64') + ':' + iv.toString('base64');
    return encryptedRes;
  }

  async getReferenceDataSettingsColumn(name) {
    let setting = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { name: name });

    if (setting) {
      return setting.value;
    } else {
      return null;
    }
  }

  async getBryzosVendorKey(vendor, key) {
    const _key = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataKeysRepository, {vendor: vendor, key: key});

    if (_key) {
      return _key.value;
    } else {
      return null;
    }
  }

  async getKeyManager(secretManagerKey) {
    let secretUserAgent = process.env.SM_BASE_URL_USER_AGENT;
    let secretName = process.env.SM_ENV;
    let response = null;
    try {
      response = (await axios.post(`${process.env.SM_BASE_URL}/getValue`, { env_name: secretName, sm_key: secretManagerKey }, { headers: { 'User-Agent': secretUserAgent, 'x-api-key': process.env.SM_API_KEY, 'Content-Type': 'application/json' } })).data;
    } catch (err) {
      this.logInfo(err);
    }
    return response;
  }

  logInfo(err: any) {
    BryzosLogger.log(JSON.stringify(err?.message || err), process.env.CASS_LOGGLY_TAG);
  }

    async getPaymentMethodId(userType, paymentType = '') {
    let paymentMethodIds = null;
    const paymentMethodQuery = paymentType ? ` AND payment_method = '${paymentType}'` : '';

    let paymentMethod = await this.dbServiceObj.findManyWithWhere(this.referenceDataPGPMMappingRepository, "status", "1", "user_type", userType);

    if (paymentMethod) {
      paymentMethodIds = paymentMethod.map((pMethod) => pMethod.id);
    }

    return paymentMethodIds;
  }

  async getCassCode(sellerPaymentMethod) {
    let cassCode = null;
    let cassPayment = (await this.dataSource.query(`Select * from reference_data_pgpm_mapping LEFT JOIN reference_data_cass_disbursement_method ON reference_data_cass_disbursement_method.pgpm_mapping_id = reference_data_pgpm_mapping.id  WHERE payment_method='${sellerPaymentMethod}' LIMIT 0, 1`))?.[0];
    if (cassPayment) {
      cassCode = cassPayment.cass_code;
    }
    return cassCode;
  }

  async getBryzosVendorSecret(vendor, key) {
    //get secret key
    const secret: any = (await this.dataSource.query("SELECT * FROM reference_data_keys WHERE vendor = '" + vendor + "' AND `key` = '" + key + "' AND is_active = 1 LIMIT 0, 1"))?.[0];

    if (secret?.secret) {
      //decrypt secret
      let encryptionKey = process.env.VALUE_ENCRYPTION_KEY;
      let encryptionType = process.env.VALUE_ENCRYPTION_TYPE;
      let parts = secret.secret.split(':');
      if (parts.length == 2) {
        let encryptedText = Buffer.from(parts[0], 'base64');
        let iv = Buffer.from(parts[1], 'base64');

        const decipher = createDecipheriv(encryptionType, encryptionKey, iv);
        const decrypted_secret = Buffer.concat([
          decipher.update(encryptedText),
          decipher.final(),
        ]).toString();
        this.logInfo(decrypted_secret);
        this.logInfo('decrypted_secret');

        return decrypted_secret;
      }
    }
    return null;
  }

  async checkBuyerCreditLimit(price,buyerId){
    let response = null;
    const companyBuyNowPayLater = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{"user_id" : buyerId});
    if(companyBuyNowPayLater && companyBuyNowPayLater.is_approved == true){
      const currentBuyerCreditAvailableLimit = Number(companyBuyNowPayLater.bryzos_available_credit_limit);
      const bryzosCreditLimit = Number(companyBuyNowPayLater.bryzos_credit_limit);
      if(currentBuyerCreditAvailableLimit > price){
        //await this.updateBryzosCreditLimit(currentBuyerCreditAvailableLimit,price,bryzosCreditLimit,buyerId);
        response = companyBuyNowPayLater;
      }else{
        response = {"error_message" : "Insufficient available credit limit!"};
      }
    }else{
      response = {"error_message" : "The buyer's BNPL is not setup."};
    }
    return response;
  }

  async updateBryzosCreditLimit(availableCreditLimit,price,bryzosCreditLimit,buyerId){
    const updateCBNPLData = {};
    const newAvailableCreditLimit = Number(availableCreditLimit) - Number(price);
    updateCBNPLData['bryzos_available_credit_limit'] = newAvailableCreditLimit
    updateCBNPLData['bryzos_outstanding_credit_limit'] =  Number(bryzosCreditLimit) - newAvailableCreditLimit;
    await this.dbServiceObj.updateByMultipleWhere(updateCBNPLData, {"user_id":buyerId}, this.companyBuyNowPayLaterRepository);
  }


  sendDataToWebsocket = (object:object,url:string)  => new Promise(async function (resolve, reject)
  {
    let curlResponse = null;
    const websocketControllerEndPoint = process.env.GISS_WS_SERVER+'/'+url;

    let updateData = {
      method: 'post',
      maxBodyLength: Infinity,
      url: websocketControllerEndPoint,
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'gissToken':  process.env.GISS_UI_TOKEN
      },
      data: object
    };
    axios.request(updateData)
    .then((response) => {
      curlResponse = response.data;
      BryzosLogger.log(JSON.stringify({"webScoketResponse":curlResponse}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      resolve(curlResponse);
    })
    .catch((error) => {
      BryzosLogger.log(JSON.stringify({"Error":curlResponse}), process.env.LOGGLY_ERROR_TAG);
    resolve(curlResponse);
    });
  });

  async getTotalCountAndAmountTransactionsForLast12Month(state_id,zipCode,userPurchaseOrderEntity){
    let totalCountAndAmount = [];
    const currentDate = new Date(); // get current date
    const startDate = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate());
    const oneYearAgo = new Date(startDate.toISOString().split('T')[0] + 'T00:00:00.000Z');

    const columnContditions = [
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: 'state_id', operator: '=', value: state_id},
      { columnName: 'zip', operator: '=', value: zipCode },
      { columnName: 'is_closed_buyer', operator: '=', value: true },
      { columnName: 'created_date', operator: '>', value: oneYearAgo }
    ]

    const selectedFields = ["count(*) as totalCount","SUM( CASE WHEN actual_buyer_po_price IS NULL THEN buyer_po_price ELSE actual_buyer_po_price END ) as totalAmount"];
    totalCountAndAmount = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(userPurchaseOrderEntity,columnContditions,selectedFields);

    if(totalCountAndAmount && totalCountAndAmount.length){
      return totalCountAndAmount[0];
    }else{
      return false;
    }
  }

  async sendWebsocketEvent(data: any, socketEndPoint: string) {
    const url = `${process.env.GISS_WS_SERVER}/${socketEndPoint}`;

    const payload = data;
    const headers = {
      'Content-Type': 'application/json',
      'gissToken': process.env.GISS_UI_TOKEN,
      'origin': process.env.AD_ORIGIN,
      'referer': process.env.AD_REFERER,
    };

    try {
      const response = (await this.httpService.axiosRef.post(url, payload, { headers })).data;
      BryzosLogger.log(JSON.stringify({ response_type: "WS response", response }), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      return response;
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error_type: "WS error", error }), process.env.LOGGLY_ERROR_TAG);
      ExceptionService.log(error);
    }
  }

  async saveUploadedFile(fileBuffer: Buffer, fileType: string, uploadId: string, originalName: string) {
    try {
        let uploadDir = './uploads';

        await this.ensureUserDirectories();

        // Create upload directory
        uploadDir = path.join(uploadDir, fileType, uploadId); // uploads/search/upload_123
        await this.ensureDirectoryExists(uploadDir);

         // Create sheets directory
        const sheetsDir = path.join(uploadDir, 'sheets'); // uploads/search/upload_123/sheets
        await this.ensureDirectoryExists(sheetsDir);

        // Create exports directory
        const exportsDir = path.join(uploadDir, 'exports'); // uploads/search/upload_123/exports
        await this.ensureDirectoryExists(exportsDir);

        // Save original file
        const filePath = path.join(uploadDir, 'original.xlsx'); // uploads/search/upload_123/original.xlsx
        await writeFile(filePath, fileBuffer);

        // Save metadata
        const metadata = {
          originalName,
          uploadDate: new Date().toISOString(),
          fileType,
          status: 'uploaded',
        };
        await writeFile(
          path.join(uploadDir, 'metadata.json'), // uploads/search/upload_123/metadata.json
          JSON.stringify(metadata, null, 2),
        );

        return filePath;

    } catch (error) {
      console.log(`Error saving uploaded file: ${error}`);
      return { error_message: `Error saving uploaded file: ${error?.message}` };
    }
  }


  async ensureUserDirectories() {
    const uploadDir = './uploads';  // uploads
    const searchDir = path.join(uploadDir, 'search');  // uploads/search
    const pricingDir = path.join(uploadDir, 'pricing'); // uploads/pricing

    await this.ensureDirectoryExists(uploadDir);
    await this.ensureDirectoryExists(searchDir);
    await this.ensureDirectoryExists(pricingDir);
  }

  async ensureDirectoryExists(dir: string) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async getFileInfo(uploadId: string) {
    try {
      console.log("getFileInfo uploadId : ",uploadId);
      // Find the upload directory
      const uploadDir = await this.findUploadDir(uploadId);
      console.log("uploadDir : ",uploadDir);
      if (!uploadDir) {
        return null;
      }

      // Read metadata
      const metadataPath = path.join(uploadDir, 'metadata.json'); // uploads/search/upload_123/metadata.json
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8')); // metadata.json

      return {
        originalFilePath: path.join(uploadDir, 'original.xlsx'), // uploads/search/upload_123/original.xlsx
          fileType: metadata.fileType, // search
      };
    } catch (error) {
      console.log(error);
      return { error_message: 'Failed to get file info' };
    }
  }

  async findUploadDir(uploadId: string) {
    // Search for the upload directory in all user directories
    try {
      const uploadDir = './uploads';  // uploads

      const uploadDirStat = await stat(uploadDir); // uploads

      if (uploadDirStat.isDirectory()) {
        const uploadDirList = await readdir(uploadDir); // uploads
        console.log(uploadDirList);

        for (const fileType of ['search', 'pricing']) {
          const typeDir = path.join(uploadDir, fileType); // uploads/search
          console.log("typeDir : ",typeDir);
          console.log("fileType : ",fileType);

          try {
            if (!uploadId.startsWith(`${fileType}_upload_`)) {
              console.log(`uploadId doesn't start with ${fileType}_upload_, continue searching`);
              continue;
            }

            const typeStat = await stat(typeDir);

            if (typeStat.isDirectory()) {
              const uploadDir = path.join(typeDir, uploadId); // uploads/search/upload_123
              console.log("uploadDir : ",uploadDir);
              try {
                const uploadStat = await stat(uploadDir); // uploads/search/upload_123

                if (uploadStat.isDirectory()) {
                  return uploadDir; // uploads/search/upload_123
                }
              } catch (error) {
                // Upload directory doesn't exist, continue searching
                console.log("findUploadDir catch-1 error (Upload directory doesn't exist, continue searching) : ",error);
              }
            }
          } catch (error) {
            // Type directory doesn't exist, continue searching
            console.log("findUploadDir catch-2 error (Type directory doesn't exist, continue searching) : ",error);
          }
        }
      }
    } catch (error) {
      // Error reading upload directory
      console.log("findUploadDir error : ",error);
    }
    return null;
  }

  async processExcelFile( filePath: string, fileType: string, uploadId: string, ) {
    try {
      console.log( `Starting processing of ${fileType} file for upload ID: ${uploadId}`, );

      // Update status to processing
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 0, message: 'Starting processing', });

      // Emit status update to websocket
      // this.emitStatusUpdate(uploadId, { status: 'processing', progress: 0, message: 'Starting processing', });

      // Process file based on type
      if (fileType === 'search') {
        await this.processSearchFile(filePath, uploadId);
      } else if (fileType === 'pricing') {
        // Placeholder for pricing file processing
        await this.processPricingFile(filePath, uploadId);
      } else {
        return {error_message: `Unsupported file type: ${fileType}`}
      }

      // Update status to completed
      await this.updateProcessingStatus(uploadId, { status: 'completed', progress: 100, message: 'Processing completed', });

      // Emit status update
      // this.emitStatusUpdate(uploadId, { status: 'completed', progress: 100, message: 'Processing completed', });

      console.log(`Processing completed for upload ID: ${uploadId}`);
    } catch (error) {
      console.log(`Error processing file: ${error.message}`, error.stack);

      let errorMessage = `Error processing file: ${error.message}`;

      // Handle specific error types with more user-friendly messages
      if (error.message && error.message.includes('Invalid string length')) {
        errorMessage = 'The file is too large to process. Try splitting it into smaller files or reducing the number of columns.';
        console.log( 'JSZip string length error detected. File too large to process.', );
      }

      // Update status to error
      await this.updateProcessingStatus(uploadId, { status: 'error', progress: 0, message: errorMessage, });

      console.log(`Processing error for upload ID: ${uploadId} : ${errorMessage}`);

      // Emit status update
      // this.emitStatusUpdate(uploadId, { status: 'error', progress: 0, message: errorMessage, });
    }
  }

  async updateProcessingStatus( uploadId: string, status: { status: string; progress: number; message: string } ) {
    try {
      console.log("updateProcessingStatus uploadId : ",uploadId);
      // Find the upload directory
      const uploadDir = await this.findUploadDir(uploadId);
      if (!uploadDir) {
        return {error_message: `Upload ID ${uploadId} not found`}
      }

      // Read existing metadata
      const metadataPath = path.join(uploadDir, 'metadata.json');
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8'));

      // Update status
      metadata.status = status.status;
      metadata.progress = status.progress;
      metadata.message = status.message;
      metadata.lastUpdated = new Date().toISOString();

      console.log("updateProcessingStatus metadata : ",metadata);

      // Save updated metadata
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.log(error);
      return {error_message: `Error updating processing status: ${error?.message}`}
    }
  }

  // wbsocket event emit
  emitStatusUpdate( uploadId: string, status: { status: string; progress: number; message: string; }, ) {
    // this.server.to(uploadId).emit('status', {
    //   uploadId,
    //   ...status,
    // });
  }

  private async processSearchFile( filePath: string, uploadId: string, ) {
    try {
      console.log(`Processing search file: ${filePath}`);

        // Hardcoded column mappings for search data
      let searchColumnMappings = [
        { source: 'Shape ID', target: 'Shape_ID' },
        { source: 'Product ID', target: 'Product_ID' },
        { source: 'Order_Increment_(Ft)', target: 'Order_Increment_Ft' },
        { source: 'Order_Increment_(Ea)', target: 'Order_Increment_Ea' },
        { source: 'Order_Increment_(Lb)', target: 'Order_Increment_Lb' },
        { source: 'Order_Increment_(CWT)', target: 'Order_Increment_CWT' },
        { source: 'Order_Increment_(Net_Ton)', target: 'Order_Increment_Net_Ton' },
        { source: 'Key_1', target: 'Key1' },
        { source: 'Key_2', target: 'Key2' },
        { source: 'Key_3', target: 'Key3' },
        { source: 'Key_4', target: 'Key4' },
        { source: 'Key_5', target: 'Key5' },
        { source: 'Key_6', target: 'Key6' },
        { source: 'Key_7', target: 'Key7' },
        { source: 'Key_8', target: 'Key8' },
        { source: 'Key_9', target: 'Key9' },
        { source: 'Key_10', target: 'Key10' },
        { source: 'Key_11', target: 'Key11' },
        { source: 'Key_12', target: 'Key12' },
        { source: 'Key_13', target: 'Key13' },
        { source: 'Key_14', target: 'Key14' },
        { source: 'Key_15', target: 'Key15' },
        { source: 'Key_16', target: 'Key16' },
        { source: 'Key_17', target: 'Key17' },
        { source: 'Key_18', target: 'Key18' },
        { source: 'Key_19', target: 'Key19' },
        { source: 'Key_20', target: 'Key20' },
        { source: 'Key_21', target: 'Key21' },
        { source: 'Key_22', target: 'Key22' },
        { source: 'Key_23', target: 'Key23' },
        { source: 'Key_24', target: 'Key24' },
        { source: 'UI_Description', target: 'UI_Description' },
        { source: 'QUM_Dropdown_Options', target: 'QUM_Dropdown_Options' },
        { source: 'PUM_Dropdown_Options', target: 'PUM_Dropdown_Options' },
        { source: 'Domestic Enabled', target: 'domestic_material_only' },
      ];

      // Extract source column names for lookup in the Excel file
      const columnsToExtract = searchColumnMappings.map(
        (mapping) => mapping.source,
      );

      // Load workbook
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);

      // Get total number of sheets
      const totalSheets = workbook.worksheets.length;
      let processedSheets = 0;
      console.log("totalSheets : ",totalSheets);

      // Track exported files for metadata
      const exportedFiles: Array<{ fileName: string; sheetName: string }> = [];
      console.log("exportedFiles : ",exportedFiles);

      // Process each sheet
      for (const worksheet of workbook.worksheets) {
        try {
          console.log(`Processing sheet: ${worksheet.name}`);

          // Update status
          const progress = Math.floor((processedSheets / totalSheets) * 100);
          await this.updateProcessingStatus(uploadId, {
            status: 'processing',
            progress,
            message: `Processing sheet ${processedSheets + 1} of ${totalSheets}: ${worksheet.name}`,
          });

          console.log(`Processing sheet ${processedSheets + 1} of ${totalSheets}: ${worksheet.name}`);

          // Emit status update to websocket
          // this.emitStatusUpdate(uploadId, { status: 'processing', progress, message: `Processing sheet ${processedSheets + 1} of ${totalSheets}: ${worksheet.name}`, });

          // Create a new workbook for the exported data
          const outputWorkbook = new ExcelJS.Workbook();
          const outputWorksheet = outputWorkbook.addWorksheet(worksheet.name);

          // Map column names to indices
          const headerIndices: { [key: string]: number } = {};
          let headerRow = worksheet.getRow(1);

          headerRow.eachCell((cell, colNumber) => {
            if (cell.value) {
              // Normalize the column name for comparison
              const normalizedValue = String(cell.value).trim();

              // Check if this column is one we want to extract
              for (const colName of columnsToExtract) {
                // Normalize the column name for comparison
                const normalizedColName = String(colName).trim();

                if (normalizedValue === normalizedColName) {
                  headerIndices[colName] = colNumber;
                  break;
                }
              }
            }
          });

          // Check if all required columns exist
          const missingColumns = columnsToExtract.filter(
            (col) => !(col in headerIndices),
          );

          if (missingColumns.length > 0) {
            console.log( `The following required columns are missing in sheet ${worksheet.name}: ${missingColumns.join(', ')}`, );

            // Continue with next sheet
            continue;
          }

          console.log( `Found all required columns in sheet ${worksheet.name}. Processing data...`, );

          // Add header row to output worksheet with target column names
          const targetColumnNames = searchColumnMappings.map(
            (mapping) => mapping.target,
          );

          // Add the new is_safe_product_code column
          targetColumnNames.push('is_safe_product_code');
          outputWorksheet.addRow(targetColumnNames);

          // Process data rows
          let rowCount = 0;
          let outputRowCount = 1; // Start at 1 because we already added the header row

          // Process each row
          for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
            const row = worksheet.getRow(rowNumber);
            rowCount++;

            // Extract values for each column
            const values = columnsToExtract.map((colName) => {
              const cellIndex = headerIndices[colName];
              if (cellIndex === undefined) return null;

              const cell = row.getCell(cellIndex);
              let cellValue = cell.value;

              // Handle formula results - get the calculated value
              if (cellValue && typeof cellValue === 'object') {
                const cellValueObj = cellValue as any;
                if ('formula' in cellValueObj) {
                  return cellValueObj.result;
                } else if ('text' in cellValueObj) {
                  return cellValueObj.text;
                } else if ('value' in cellValueObj) {
                  return cellValueObj.value;
                }
              }

              return cellValue;
            });

            // Find the Product_ID value to determine is_safe_product_code
            const productIdIndex = columnsToExtract.findIndex(
              (col) => col === 'Product ID',
            );

            let productId = values[productIdIndex];

            // Handle formula results or objects for Product ID
            if (productId && typeof productId === 'object') {
              const productIdObj = productId as any;
              if ('formula' in productIdObj) {
                productId = productIdObj.result;
              } else if ('text' in productIdObj) {
                productId = productIdObj.text;
              } else if ('value' in productIdObj) {
                productId = productIdObj.value;
              }
            }

            // Convert to string for comparison
            productId = String(productId || '');

            // Set is_safe_product_code to 1 if Product_ID is 421020001 or 422020001, otherwise 0
            const isSafeProductCode =
              productId === '421020001' || productId === '422020001' ? 1 : 0;

            // Add the is_safe_product_code value to the row
            values.push(isSafeProductCode);

            // Add row to output worksheet
            outputWorksheet.addRow(values);
            outputRowCount++;

            // Log progress
            if (rowCount % 1000 === 0) {
              console.log( `Processed ${rowCount} rows in sheet ${worksheet.name}...`, );
            }
          }

          console.log( `Processed ${rowCount} rows from sheet ${worksheet.name}.`, );
          console.log( `Added ${outputRowCount} rows to output file (including header).`, );

          // Save the output workbook
          // const outputFileName = `${worksheet.name}_product_search.xlsx`;
          const outputFileName = `product_search.xlsx`;
          const outputPath = await this.getExportedFilePath( './uploads', 'search', uploadId, outputFileName, );

          await outputWorkbook.xlsx.writeFile(outputPath);
          console.log(`Saved exported file to: ${outputPath}`);

          // Add to exported files list
          exportedFiles.push({
            fileName: outputFileName,
            sheetName: worksheet.name,
          });

          processedSheets++;
        } catch (error) {
          console.log( `Error processing sheet ${worksheet.name}:`, error);
          // Continue with next sheet
        }
      }

      // Update metadata with exported files
      await this.updateMetadataWithExportedFiles( uploadId, exportedFiles );

      console.log( `Completed processing search file. Processed ${processedSheets} of ${totalSheets} sheets.`, );
    } catch (error) {
      console.log( `Error processing search file:`, error);
    }
  }

  async getExportedFilePath( uploadDir: string, fileType: string, uploadId: string, fileName: string, ) {
    const exportsDir = path.join(uploadDir, fileType, uploadId, 'exports', );
    await this.ensureDirectoryExists(exportsDir);
    return path.join(exportsDir, fileName);
  }

  async updateMetadataWithExportedFiles( uploadId: string, exportedFiles: Array<{ fileName: string; sheetName: string }>, ) {
    try {
      console.log("updateMetadataWithExportedFiles uploadId : ",uploadId);
      // Find the upload directory
      const uploadDir = await this.findUploadDir(uploadId);
      if (!uploadDir) {
        console.log( `Upload ID ${uploadId} not found`);
        throw new Error(`Upload ID ${uploadId} not found`);
      }

      // Read existing metadata
      const metadataPath = path.join(uploadDir, 'metadata.json');
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8'));

      // Update exported files
      metadata.exportedFiles = exportedFiles;
      metadata.exportedAt = new Date().toISOString();

      // Save updated metadata
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.log( `Error updating metadata with exported files:`, error);
    }
  }

  private async processPricingFile( filePath: string, uploadId: string, ) {
    console.log(`Processing pricing file: ${filePath}`);

    // Track exported files for metadata
    const exportedFiles: Array<{ fileName: string; sheetName: string }> = [];

    try {
      // Process volume_pricing_brackets sheet
      console.log('Processing volume_pricing_brackets sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 10, message: 'Processing volume_pricing_brackets sheet', });

      console.log("Processing volume_pricing_brackets sheet");
      // this.emitStatusUpdate(uploadId, { status: 'processing', progress: 10, message: 'Processing volume_pricing_brackets sheet', });

      // Hardcoded column mappings for pricing data - volume_pricing_brackets
      let volumePricingBracketsColumnMappings = [
        { source: 'bracket', target: 'bracket' },
        { source: 'low_value_lbs', target: 'low_value_lbs' },
        { source: 'high_value_lbs', target: 'high_value_lbs' },
        { source: 'default_bracket', target: 'default_bracket' },
        { source: 'ui_dropdown_choice', target: 'ui_dropdown_choice' },
        { source: 'value', target: 'value' },
      ];

      const volumePricingResult = await this.excelSheetWrapper.processExcelSheet( filePath, 'volume_pricing_brackets', 'volume_pricing_brackets.xlsx', volumePricingBracketsColumnMappings, uploadId, );
      console.log("volumePricingResult: ",volumePricingResult);

      if (volumePricingResult) {
        exportedFiles.push({
          fileName: 'volume_pricing_brackets.xlsx',
          sheetName: 'volume_pricing_brackets',
        });
      }
      console.log("volumePricingResult-2: ",volumePricingResult);

      // Process region_lookup_by_zip sheet
      console.log('Processing region_lookup_by_zip sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 30, message: 'Processing region_lookup_by_zip sheet', });

      console.log('Processing region_lookup_by_zip sheet');

      // this.emitStatusUpdate(uploadId, { status: 'processing', progress: 30, message: 'Processing region_lookup_by_zip sheet', });

      // Hardcoded column mappings for pricing data - region_lookup_by_zip
      let regionLookupByZipColumnMappings = [
        { source: 'zip_code', target: 'zip_code' },
        { source: 'tier_1', target: 'tier_1' },
        { source: 'tier_2', target: 'tier_2' },
        { source: 'tier_3', target: 'tier_3' },
      ];

      const regionLookupResult = await this.processExcelSheet( filePath, 'region_lookup_by_zip', 'region_lookup_by_zip.xlsx', regionLookupByZipColumnMappings, uploadId, );
      console.log("regionLookupResult: ",regionLookupResult);

      if (regionLookupResult) {
        exportedFiles.push({
          fileName: 'region_lookup_by_zip.xlsx',
          sheetName: 'region_lookup_by_zip',
        });
      }
      console.log("regionLookupResult-2: ",regionLookupResult);

      // Process tiers_per_state sheet
      console.log('Processing tiers_per_state sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 50, message: 'Processing tiers_per_state sheet', });

      // this.emitStatusUpdate(uploadId, { status: 'processing', progress: 50, message: 'Processing tiers_per_state sheet', });

      // Hardcoded column mappings for pricing data - tiers_per_state
      let tiersPerStateColumnMappings = [
        { source: 'state_abbreviation', target: 'state_abbreviation' },
        { source: 'tiers', target: 'tiers' },
      ];

      const tiersPerStateResult = await this.processExcelSheet( filePath, 'tiers_per_state', 'tiers_per_state.xlsx', tiersPerStateColumnMappings, uploadId, );
      console.log("tiersPerStateResult: ",tiersPerStateResult);
      if (tiersPerStateResult) {
        exportedFiles.push({
          fileName: 'tiers_per_state.xlsx',
          sheetName: 'tiers_per_state',
        });
      }
      console.log("tiersPerStateResult-2: ",tiersPerStateResult);

      // Process regional_pricing_lookup sheet (creates files for each state)
      console.log( 'Processing regional_pricing_lookup sheet for all states', );
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 70, message: 'Processing regional_pricing_lookup sheet for all states', });

      // this.emitStatusUpdate(uploadId, { status: 'processing', progress: 70, message: 'Processing regional_pricing_lookup sheet for all states', });

      const regionalPricingResults = await this.processRegionalPricingByState( filePath, 'regional_pricing_lookup', uploadId, );
      // Add all regional pricing files to the exported files list
      exportedFiles.push(...regionalPricingResults);

      // Update metadata with exported files
      await this.updateMetadataWithExportedFiles( uploadId, exportedFiles, );

      console.log( `Completed processing pricing file. Exported ${exportedFiles.length} files.`, );
    } catch (error) {
      console.log( `Error processing pricing file: ${error.message}`, error );
      throw error;
    }
  }

  async processExcelSheet( inputFilePath: string, sheetName: string, outputFileName: string, columnMappings, uploadId: string, ) {
    try {
      console.log( `Processing sheet: ${sheetName} for export to ${outputFileName}`, );

      // Path to the JavaScript implementation
      // const jsScriptPath = path.resolve(
      //   __dirname,
      //   // '../../../sample-logic/export-excel-sheet.js',
      //   './sample-logic/export-excel-sheet.js'
      // );

      // Convert column mappings to JSON string
      const columnMappingsJson = JSON.stringify(columnMappings);
      console.log("__dirname: ",__dirname);


      const jsScriptPath = path.resolve(
        __dirname, // Use current working directory (project root)
        '../../sample-logic/export-excel-sheet.js'
      );


      // Also fix the variable naming conflict:
      const childProcess = spawn('node', [ jsScriptPath, inputFilePath, sheetName, outputFileName, columnMappingsJson, uploadId, ]);
      // console.log("childProcess: ", childProcess);
      console.log("jsScriptPath: ", jsScriptPath);


      // Spawn a child process to run the JavaScript implementation
      // const process = spawn('node', [ jsScriptPath, inputFilePath, sheetName, outputFileName, columnMappingsJson, uploadId, ]);
      // console.log("childProcess: ",childProcess);
      // console.log("jsScriptPath: ",jsScriptPath);

      // Collect output from the process
      let output = '';
      childProcess.stdout.on('data', (data) => {
        output += data.toString();
        console.log(data.toString().trim());
      });

      let errorOutput = '';
      childProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.log(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<boolean>((resolve) => {
        childProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`Successfully processed sheet: ${sheetName}`);
            resolve(true);
          } else {
            console.log( `Failed to process sheet: ${sheetName}. Exit code: ${code}`, );
            console.log(`Error output: ${errorOutput}`);
            resolve(false);
          }
        });
      });
    } catch (error) {
      console.log(`Error processing sheet ${sheetName}:`, error);
      return false;
    }
  }

  async processRegionalPricingByState( inputFilePath: string, sheetName: string, uploadId: string, ) : Promise<ExportedFile[]>{
    try {
      console.log( `Processing regional pricing by state from sheet: ${sheetName}`, );

      // Path to the JavaScript implementation
      // const jsScriptPath = path.resolve(
      //   __dirname,
      //   // '../../../sample-logic/export-regional-pricing-by-state.js',
      //   './sample-logic/export-regional-pricing-by-state.js'
      // );

      const jsScriptPath = path.resolve(
        __dirname, // Use current working directory (project root)
        '../../sample-logic/export-regional-pricing-by-state.js' // Go up one level to find sample-logic
      );

      console.log("jsScriptPath: ",jsScriptPath);

      // Spawn a child process to run the JavaScript implementation
      // const process = spawn('node', [
      //   jsScriptPath,
      //   inputFilePath,
      //   sheetName,
      //   uploadId,
      // ]);

      const childProcess = spawn('node', [ jsScriptPath, inputFilePath, sheetName, uploadId, ]);
    // console.log("childProcess: ", childProcess);
    console.log("jsScriptPath: ", jsScriptPath);

      // Collect output from the process
      let output = '';
      childProcess.stdout.on('data', (data) => {
        output += data.toString();
        console.log(data.toString().trim());
      });

      let errorOutput = '';
      childProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.log(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<ExportedFile[]>((resolve) => {
        childProcess.on('close', (code) => {
          if (code === 0) {
            console.log( `Successfully processed regional pricing by state from sheet: ${sheetName}`, );

            try {
              // Parse the output to get the exported files
              const exportedFilesMatch = output.match(/EXPORTED_FILES:(.*)/);
              if (exportedFilesMatch && exportedFilesMatch[1]) {
                const exportedFiles = JSON.parse(exportedFilesMatch[1]);
                resolve(exportedFiles);
              } else {
                console.log('No exported files found in output');
                resolve([]);
              }
            } catch (error) {
              console.log('Error parsing exported files:', error);
              resolve([]);
            }
          } else {
            console.log( `Failed to process regional pricing by state from sheet: ${sheetName}. Exit code: ${code}`, );
            console.log(`Error output: ${errorOutput}`);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.log( `Error processing regional pricing by state:`, error, );
      return [];
    }
  }

  async processExportFileAndUploadToS3(uploadId: string, fileType: 'search' | 'pricing') {
    console.log("processExportFile uploadId: ",uploadId);
    const baseDir = path.join('uploads', fileType, uploadId, 'exports');
    console.log("baseDir: ",baseDir);

    const bucket = process.env.UPLOAD_PRODUCT_PRICING_SPLIT_EXCEL_BUCKET;
    const bucketDir = process.env.S3_BUCKET_ENVIRONMENT;

    try {
      const stats = await stat(baseDir);
      if (!stats.isDirectory()) return { error: 'Exports directory not found' };
    } catch (e) {
      return { error: 'Exports directory does not exist' };
    }

    let files: string[];
    try {
      files = await readdir(baseDir);
    } catch (e) {
      return { error: 'Unable to read exports directory' };
    }

    // Get the correct file to upload
    let fileName: string | undefined;

    if (fileType === 'search') {
      fileName = files.find(name => name === 'product_search.xlsx');
    } else if (fileType === 'pricing') {
      for (const fileName of files) {
        const filePath = path.join(baseDir, fileName);
        const fileBuffer = await readFile(filePath);
        const s3Key = `${bucketDir}/${fileType}/${fileName}`;
        await this.awsUtility.uploadFileInS3(s3Key, bucket, fileBuffer);
      }

      return {
        success: true,
        message: 'All pricing files uploaded to S3 successfully',
        fileCount: files.length,
      };
    }

    if (!fileName) {
      return { error: 'No valid file found in exports directory' };
    }

    const filePath = path.join(baseDir, fileName);
    const fileBuffer = await readFile(filePath);

    const s3Key = `${bucketDir}/${fileType}/${fileName}`;
    console.log("s3Key: ",s3Key);
    // Upload to S3
    await this.awsUtility.uploadFileInS3(s3Key, bucket, fileBuffer);

    return {
      success: true,
      message: 'File uploaded to S3 successfully',
      s3Key,
      localPath: filePath,
    };
  }
}