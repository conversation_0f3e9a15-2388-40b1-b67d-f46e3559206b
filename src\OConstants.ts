import { CustomDepositDetails, GlobalDepositDetails, UserBuyingPreference, UserAchCredit, UserPurchaseOrder, ReferenceDataOrderStatus, UserPurchaseOrderLine, UserPurchaseOrderLedger, CancelOrderLogs, ReferenceDyspatchTemplates, AdminLogCloseOrders, AdminLogUpdateOrderQuantity, UserResaleCertificate, CompanyBuyNowPayLater, LogAuthAmount,ReferenceDataStates, ReferenceDataSalesTax, ReferenceEmailEvent, AdminLogEmailGenerate, UserOnboradPendingRequests, LogBryzosCreditLimit, AdminLogNotification, ReferenceDataDesktopNotification, UserPendingCompanyRequests,UserMainCompany,UserSellingPreference, AdminLogInvoiceEmail, AdminLogSetPassword, User, AdminLogFlipAchToBnpl, ReferenceDataUserOnboardedAppVersion, HomepageSafeUploads, HomepageSafeConfig, TaxExemptedPurchaseOrders, AdminLogSpread, ReferenceDataSpreadDefaultValues,OrderLineSnapshotPricing,AdminLogUpdateOrderDetails, UserViewedPurchaseOrder,ReferenceDataResaleCertExpiration,UserRequestIncreaseCredits, LogCassSupplier, PaymentInfo, ReferenceDataGeneralSettings, UserArPaymentInfo, CassTransactionView, LogCassPaymentStatus, UserPurchaseOrderProcessingHistory, ReferenceDataPGPMMapping, AdminLogResalesCertificate, AdminLogRemoveSalesTax, WidgetUser, EmailVerificationStatus, CassAdhocSellerSetup, ReferenceDataProductsWidget, UserProductTagMapping, ReferenceDataSellerMinPricing, SellerPriceUpdateCalculationsLog, UserOrderDepoistLogs, AdminLogUpdateOrderLineDetails, ReferenceDataRecipientEmailSettings, CassFundingReceivedStatus, CassTransactionEmail, CompanyResaleCertificate, SpreadActiveUsersView, CassMappingProbalePosView, ReferenceDataUiReleaseUrls, ReferenceDataBryzosTermsConditions, HomepageSafeUploadComments, ExtendedWidgetVideoLibrary, ReferenceDataVideoLibraryTag, SellerInvoiceDump, AdminLogBryzosPayCreditLimit, BuyerInBalance, AdminLogAchCreditPoRequest, AdminLogUserUpdate, ReferenceDataDeliveryReceivingAvailabilityDetails, ReferenceDataStateZipcode, UserDeliveryReceivingAvailabilityDetails, WidgetTermsCondtionUserActions, SignUpPreApprovedEmail, CassFinancialTransactionStatus, CassBuyerAmountReceivedPerPO, ExtendedWidgetHolidayCalendar, OrderUtilityLibrary, ReferenceDataDeliveryDate, SignupUtility, SecretManagerUtility, ExtendedWidgetLibraryService, UserLogger, ReferenceDataExternalApiEndpoint, ExternalApiKey, SandboxExternalApiKey, ReferenceDataBomProductConfidence, ReferenceDataSubscriptionPricingTiers, UserSubscriptionEmailInvitations } from "@bryzos/extended-widget-library";
import { CashInAdvanceDepositService } from "./cash-in-advance-deposit/cash-in-advance-deposit.service";
import { CancelOrderService } from "./cancel-order/cancel-order.service";
import { DyspatchService } from "./dyspatch/dyspatch.service";
import { CloseOrderService } from "./close-order/close-order.service";
import { UserService } from "./user/user.service";
import { ReferenceDataKeys, ReferenceDataSettings, AwsUtilityV3, DataBaseService } from '@bryzos/base-library';
import { Balance } from "./Balance";
import { AwsQueue } from "./AwsQueue";
import { Utils } from "./utils";
import { EmailGenerateService } from "./email_generate/email_generate.service";
import { ResaleCertificateService } from "./widget-admin-dashboard/resale-certificate/resale-certificate.service";
import { CassService } from "./cass/cass.service";
import { CassApis } from "./cass/CassApis";
import { UserAchCreditService } from "./widget-admin-dashboard/user-ach-credit/user-ach-credit.service";
import { WidgetAdminDashboardService } from "./widget-admin-dashboard/widget-admin-dashboard.service";
import { RemoveSalesTaxService } from "./remove-sales-tax/remove-sales-tax.service";
import { OrderService } from "./order/order.service";
import { CompanyBuyNowPayLaterService } from "./widget-admin-dashboard/company-buy-now-pay-later.service";
import { ExcelSheetWrapper } from "./export-excel-sheet-wrapper";

export class OConstants {
    public static EntityArray = [CustomDepositDetails,GlobalDepositDetails, UserBuyingPreference, UserAchCredit, ReferenceDataSettings, UserPurchaseOrder, ReferenceDataOrderStatus, UserPurchaseOrderLine, UserPurchaseOrderLedger, CancelOrderLogs, ReferenceDyspatchTemplates, AdminLogCloseOrders, AdminLogUpdateOrderQuantity, UserResaleCertificate, CompanyBuyNowPayLater, LogAuthAmount,ReferenceDataStates, ReferenceDataSalesTax, ReferenceEmailEvent, AdminLogEmailGenerate, UserOnboradPendingRequests, LogBryzosCreditLimit, AdminLogNotification, ReferenceDataDesktopNotification,UserPendingCompanyRequests,UserMainCompany,UserSellingPreference,AdminLogInvoiceEmail,User,AdminLogSetPassword, AdminLogFlipAchToBnpl,ReferenceDataUserOnboardedAppVersion, HomepageSafeUploads, HomepageSafeConfig, TaxExemptedPurchaseOrders,AdminLogSpread, ReferenceDataSpreadDefaultValues,OrderLineSnapshotPricing,AdminLogUpdateOrderDetails, UserViewedPurchaseOrder,ReferenceDataResaleCertExpiration,UserRequestIncreaseCredits, ReferenceDataPGPMMapping, ReferenceDataKeys, CassTransactionView, ReferenceDataGeneralSettings, PaymentInfo, UserArPaymentInfo, UserPurchaseOrderProcessingHistory, LogCassPaymentStatus, LogCassSupplier, AdminLogResalesCertificate, AdminLogRemoveSalesTax, WidgetUser, EmailVerificationStatus, CassAdhocSellerSetup, ReferenceDataProductsWidget, ReferenceDataSellerMinPricing, SellerPriceUpdateCalculationsLog, UserOrderDepoistLogs, UserProductTagMapping, AdminLogUpdateOrderLineDetails, ReferenceDataRecipientEmailSettings, CassFundingReceivedStatus, CassTransactionEmail, CompanyResaleCertificate, SpreadActiveUsersView, CassMappingProbalePosView, ReferenceDataUiReleaseUrls, ReferenceDataBryzosTermsConditions,HomepageSafeUploadComments, ExtendedWidgetVideoLibrary, ReferenceDataVideoLibraryTag, SellerInvoiceDump, AdminLogBryzosPayCreditLimit, BuyerInBalance, AdminLogAchCreditPoRequest, AdminLogUserUpdate, ReferenceDataStateZipcode, ReferenceDataDeliveryReceivingAvailabilityDetails, UserDeliveryReceivingAvailabilityDetails, WidgetTermsCondtionUserActions, SignUpPreApprovedEmail, CassFinancialTransactionStatus,CassBuyerAmountReceivedPerPO, ExtendedWidgetHolidayCalendar,ReferenceDataDeliveryDate, UserLogger, ReferenceDataExternalApiEndpoint, ExternalApiKey, SandboxExternalApiKey, ReferenceDataBomProductConfidence,ReferenceDataSubscriptionPricingTiers, UserSubscriptionEmailInvitations];

    public static ServiceArray = [CashInAdvanceDepositService, CancelOrderService, Balance, AwsQueue, DyspatchService, CloseOrderService, UserService, Utils, AwsUtilityV3, EmailGenerateService,ResaleCertificateService, CassService, CassApis, UserAchCreditService, WidgetAdminDashboardService, RemoveSalesTaxService, OrderService, CompanyBuyNowPayLaterService, OrderUtilityLibrary, SignupUtility, SecretManagerUtility, ExtendedWidgetLibraryService, ExcelSheetWrapper];

}


