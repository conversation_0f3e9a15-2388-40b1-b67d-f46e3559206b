import { spawn } from 'child_process';
import * as path from 'path';
import { Logger } from '@nestjs/common';

/**
 * Interface for column mapping
 */
export interface ColumnMapping {
  source: string;
  target: string;
}

/**
 * Interface for exported file information
 */
export interface ExportedFile {
  fileName: string;
  sheetName: string;
}

/**
 * Wrapper for the JavaScript Excel sheet processing functionality
 */
export class ExcelSheetWrapper {
  private readonly logger = new Logger(ExcelSheetWrapper.name);

  /**
   * Process an Excel sheet and export it to a new file
   * @param inputFilePath Path to the input Excel file
   * @param sheetName Name of the sheet to process
   * @param outputFileName Name of the output file
   * @param columnMappings Array of column mappings
   * @param email User email
   * @param uploadId Upload ID
   * @returns Promise that resolves to true if successful, false otherwise
   */
  async processExcelSheet( inputFilePath: string, sheetName: string, outputFileName: string, columnMappings: ColumnMapping[], uploadId: string, ): Promise<boolean> {
    try {
      this.logger.log(
        `Processing sheet: ${sheetName} for export to ${outputFileName}`,
      );

      // Path to the JavaScript implementation
      const jsScriptPath = path.resolve(
        __dirname,
        '../../../sample-logic/export-excel-sheet.js',
      );

      // Convert column mappings to JSON string
      const columnMappingsJson = JSON.stringify(columnMappings);

      // Spawn a child process to run the JavaScript implementation
      const process = spawn('node', [
        jsScriptPath,
        inputFilePath,
        sheetName,
        outputFileName,
        columnMappingsJson,
        uploadId,
      ]);

      // Collect output from the process
      let output = '';
      process.stdout.on('data', (data) => {
        output += data.toString();
        this.logger.log(data.toString().trim());
      });

      let errorOutput = '';
      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
        this.logger.error(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<boolean>((resolve) => {
        process.on('close', (code) => {
          if (code === 0) {
            this.logger.log(`Successfully processed sheet: ${sheetName}`);
            resolve(true);
          } else {
            this.logger.error(
              `Failed to process sheet: ${sheetName}. Exit code: ${code}`,
            );
            this.logger.error(`Error output: ${errorOutput}`);
            resolve(false);
          }
        });
      });
    } catch (error) {
      this.logger.error(`Error processing sheet ${sheetName}:`, error.stack);
      return false;
    }
  }

  /**
   * Process regional pricing by state
   * @param inputFilePath Path to the input Excel file
   * @param sheetName Name of the sheet to process
   * @param email User email
   * @param uploadId Upload ID
   * @returns Promise that resolves to an array of exported files
   */
  async processRegionalPricingByState(
    inputFilePath: string,
    sheetName: string,
    email: string,
    uploadId: string,
  ): Promise<ExportedFile[]> {
    try {
      this.logger.log(
        `Processing regional pricing by state from sheet: ${sheetName}`,
      );

      // Path to the JavaScript implementation
      const jsScriptPath = path.resolve(
        __dirname,
        '../../../sample-logic/export-regional-pricing-by-state.js',
      );

      // Spawn a child process to run the JavaScript implementation
      const process = spawn('node', [
        jsScriptPath,
        inputFilePath,
        sheetName,
        email,
        uploadId,
      ]);

      // Collect output from the process
      let output = '';
      process.stdout.on('data', (data) => {
        output += data.toString();
        this.logger.log(data.toString().trim());
      });

      let errorOutput = '';
      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
        this.logger.error(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<ExportedFile[]>((resolve) => {
        process.on('close', (code) => {
          if (code === 0) {
            this.logger.log(
              `Successfully processed regional pricing by state from sheet: ${sheetName}`,
            );
            
            try {
              // Parse the output to get the exported files
              const exportedFilesMatch = output.match(/EXPORTED_FILES:(.*)/);
              if (exportedFilesMatch && exportedFilesMatch[1]) {
                const exportedFiles = JSON.parse(exportedFilesMatch[1]);
                resolve(exportedFiles);
              } else {
                this.logger.warn('No exported files found in output');
                resolve([]);
              }
            } catch (error) {
              this.logger.error('Error parsing exported files:', error.stack);
              resolve([]);
            }
          } else {
            this.logger.error(
              `Failed to process regional pricing by state from sheet: ${sheetName}. Exit code: ${code}`,
            );
            this.logger.error(`Error output: ${errorOutput}`);
            resolve([]);
          }
        });
      });
    } catch (error) {
      this.logger.error(
        `Error processing regional pricing by state:`,
        error.stack,
      );
      return [];
    }
  }
}
