#!/usr/bin/env node

// Import required libraries
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);

// Show usage information if arguments are missing
if (args.length < 4) {
  console.log(
    'Usage: node export-regional-pricing-by-state.js <input_excel_path> <sheet_name> <email> <uploadId>',
  );
  console.log(
    'Example: node export-regional-pricing-by-state.js ./products.xlsx "regional_pricing_lookup" "<EMAIL>" "upload_123"',
  );
  process.exit(1);
}

// Extract arguments
const inputFilePath = args[0];
const sheetName = args[1];
const email = args[2];
const uploadId = args[3];

// List of states to filter on
const statesToFilter = [
  'AK',
  'AL',
  'AR',
  'AZ',
  'CA',
  'CO',
  'CT',
  'DC',
  'DE',
  'FL',
  'GA',
  'IA',
  'ID',
  'IL',
  'IN',
  'KS',
  'KY',
  'LA',
  'MA',
  'MD',
  'ME',
  'MI',
  'MN',
  'MO',
  'MS',
  'MT',
  'NC',
  'ND',
  'NE',
  'NH',
  'NJ',
  'NM',
  'NV',
  'NY',
  'OH',
  'OK',
  'OR',
  'PA',
  'RI',
  'SC',
  'SD',
  'TN',
  'TX',
  'UT',
  'VA',
  'VT',
  'WA',
  'WI',
  'WV',
  'WY',
];

// Define the column mappings (source column name to target column name)
const columnMappings = [
  { source: 'product_id', target: 'product_id' },
  { source: 'lbs_per_ft', target: 'lbs_per_ft' },
  { source: 'order_increment_ft', target: 'order_increment_ft' },
  { source: 'mill_$_price_per_cwt', target: 'mill_price_per_cwt' },
  {
    source: 'regional_distributor_multiplier_%_tier_1',
    target: 'regional_distributor_multiplier_percent_tier_1',
  },
  {
    source: 'regional_distributor_multiplier_%_tier_2',
    target: 'regional_distributor_multiplier_percent_tier_2',
  },
];

// Extract source column names for lookup in the Excel file
const columnsToExtract = columnMappings.map((mapping) => mapping.source);
columnsToExtract.push('state_abbreviation'); // Add the filter column

// Function to export Excel sheet to multiple files by state
async function exportRegionalPricingByState(
  inputFilePath,
  sheetName,
  statesToFilter,
  columnsToExtract,
  columnMappings,
  email,
  uploadId,
) {
  try {
    // Check if input file exists
    if (!fs.existsSync(inputFilePath)) {
      console.error(
        `Error: Input file does not exist at path: ${inputFilePath}`,
      );
      process.exit(1);
    }

    console.log(`Reading input file: ${inputFilePath}`);
    console.log(`Looking for sheet: ${sheetName}`);
    console.log(`Columns to extract: ${columnsToExtract.join(', ')}`);
    console.log(`Filtering by states: ${statesToFilter.join(', ')}`);

    // Display column mappings
    console.log('Column mappings:');
    columnMappings.forEach((mapping) => {
      console.log(`  ${mapping.source} → ${mapping.target}`);
    });

    // Create the output directory structure
    const exportDir = path.join(
      'uploads',
      email,
      'pricing',
      uploadId,
      'exports',
    );
    if (!fs.existsSync(exportDir)) {
      console.log(`Creating directory: ${exportDir}`);
      fs.mkdirSync(exportDir, { recursive: true });
    }

    // Create a workbook reader for input file
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(
      inputFilePath,
      {
        sharedStrings: 'cache',
        hyperlinks: 'ignore',
        worksheets: 'emit',
        formulas: 'shared',
        styles: 'ignore',
      },
    );

    let foundSheet = false;
    let rowCount = 0;
    let headerIndices = {};
    let stateRowCounts = {}; // Track row counts per state

    // Initialize state row counts
    statesToFilter.forEach((state) => {
      stateRowCounts[state] = 0;
    });

    // Create a buffer of rows for each state to manage memory
    const stateBuffers = {};
    const BUFFER_FLUSH_SIZE = 1000; // Flush to file every 1000 rows

    // Create workbooks for each state
    const stateWorkbooks = {};

    // Process each worksheet in the input file
    for await (const worksheetReader of workbookReader) {
      // Check if this is the sheet we're looking for
      if (worksheetReader.name === sheetName) {
        foundSheet = true;
        console.log(`\nFound sheet: ${sheetName}`);

        // Process each row
        for await (const row of worksheetReader) {
          rowCount++;

          // Process header row (first row)
          if (rowCount === 1) {
            // Map column names to indices
            row.values.forEach((value, index) => {
              if (value) {
                // Handle special characters in column names
                const normalizedValue = String(value).trim();

                // Check if this column is one we want to extract
                for (const colName of columnsToExtract) {
                  // Normalize the column name for comparison
                  const normalizedColName = colName
                    .replace(/\$/g, '$')
                    .replace(/%/g, '%')
                    .trim();

                  if (normalizedValue === normalizedColName) {
                    headerIndices[colName] = index;
                    break;
                  }
                }
              }
            });

            // Check if all required columns exist
            const missingColumns = columnsToExtract.filter(
              (col) => !(col in headerIndices),
            );
            if (missingColumns.length > 0) {
              console.error(
                `Error: The following required columns are missing: ${missingColumns.join(', ')}`,
              );
              console.log(
                'Available columns:',
                row.values.filter(Boolean).join(', '),
              );
              process.exit(1);
            }

            console.log(`Found all required columns. Processing data...`);

            // Initialize workbooks and worksheets for each state
            for (const state of statesToFilter) {
              stateWorkbooks[state] = {
                workbook: new ExcelJS.Workbook(),
                worksheet: null,
                rowCount: 0,
                filePath: path.join(
                  exportDir,
                  `regional_volume_pricing_lookup_${state}.xlsx`,
                ),
              };

              // Add worksheet with target column names (excluding state_abbreviation)
              stateWorkbooks[state].worksheet =
                stateWorkbooks[state].workbook.addWorksheet(sheetName);

              // Add header row with target column names
              const targetColumnNames = columnMappings.map(
                (mapping) => mapping.target,
              );
              stateWorkbooks[state].worksheet.addRow(targetColumnNames);
              stateWorkbooks[state].rowCount++;
            }

            continue; // Skip to next row
          }

          // Get the state abbreviation for this row
          const stateAbbrevIndex = headerIndices['state_abbreviation'];
          if (!stateAbbrevIndex) {
            console.error('Error: Could not find state_abbreviation column');
            process.exit(1);
          }

          let stateAbbrev = row.values[stateAbbrevIndex];

          // Handle formula results or objects
          if (stateAbbrev && typeof stateAbbrev === 'object') {
            if (stateAbbrev.formula) {
              stateAbbrev = stateAbbrev.result;
            } else if (stateAbbrev.text) {
              stateAbbrev = stateAbbrev.text;
            } else if (stateAbbrev.value !== undefined) {
              stateAbbrev = stateAbbrev.value;
            }
          }

          // Skip if state is not in our filter list
          if (!stateAbbrev || !statesToFilter.includes(stateAbbrev)) {
            continue;
          }

          // Extract values for each column (excluding state_abbreviation)
          const values = columnMappings.map((mapping) => {
            const colName = mapping.source;
            const cellIndex = headerIndices[colName];
            if (cellIndex === undefined) return null;

            const cellValue = row.values[cellIndex];

            // Handle formula results - get the calculated value
            if (cellValue && typeof cellValue === 'object') {
              if (cellValue.formula) {
                return cellValue.result;
              } else if (cellValue.text) {
                return cellValue.text;
              } else if (cellValue.value !== undefined) {
                return cellValue.value;
              }
            }

            return cellValue;
          });

          // Add row to the appropriate state worksheet
          stateWorkbooks[stateAbbrev].worksheet.addRow(values);
          stateWorkbooks[stateAbbrev].rowCount++;
          stateRowCounts[stateAbbrev]++;

          // Log progress
          if (rowCount % 10000 === 0) {
            console.log(`Processed ${rowCount} rows...`);

            // Log state counts periodically
            const statesWithData = Object.entries(stateRowCounts)
              .filter(([_, count]) => count > 0)
              .map(([state, count]) => `${state}: ${count}`)
              .join(', ');

            console.log(`States with data so far: ${statesWithData}`);
          }
        }

        console.log(`\nProcessed ${rowCount} total rows from input file.`);

        // Save workbooks for states that have data
        const exportedFiles = [];
        for (const state of statesToFilter) {
          if (stateRowCounts[state] > 0) {
            console.log(
              `Saving ${stateRowCounts[state]} rows for ${state} to ${stateWorkbooks[state].filePath}`,
            );
            try {
              // Use writeBuffer instead of writeFile to avoid JSZip string length issues
              const buffer = await stateWorkbooks[
                state
              ].workbook.xlsx.writeBuffer({
                useStyles: true,
                useSharedStrings: false, // Disable shared strings to reduce memory usage
              });
              fs.writeFileSync(stateWorkbooks[state].filePath, buffer);
            } catch (error) {
              if (
                error.message &&
                error.message.includes('Invalid string length')
              ) {
                console.error(
                  `Error: File for state ${state} too large to process in one operation.`,
                );
                console.error(
                  'Try reducing the number of columns or implementing chunking for this state.',
                );
                // Continue with other states instead of exiting
                continue;
              }
              throw error;
            }

            // Add to exported files list
            const fileName = path.basename(stateWorkbooks[state].filePath);
            exportedFiles.push({
              fileName,
              sheetName,
            });
          } else {
            console.log(`No data for ${state}, skipping file creation`);
          }
        }

        // Output the exported files in a format that can be parsed by the wrapper
        console.log(`EXPORTED_FILES:${JSON.stringify(exportedFiles)}`);

        console.log('\nExport complete!');
        break; // Exit the worksheet loop once we've found and processed our target sheet
      }
    }

    if (!foundSheet) {
      console.error(`Error: Sheet "${sheetName}" not found in the workbook.`);
      process.exit(1);
    }
  } catch (error) {
    console.error('An error occurred while processing the Excel file:');
    console.error(error.message);
    process.exit(1);
  }
}

// Execute the function
(async () => {
  await exportRegionalPricingByState(
    inputFilePath,
    sheetName,
    statesToFilter,
    columnsToExtract,
    columnMappings,
    email,
    uploadId,
  );
})();
